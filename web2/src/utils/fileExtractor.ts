interface ExtractedFile {
  name: string;
  content: string;
  language?: string;
  status: 'generating' | 'completed' | 'error';
}

/**
 * 从 Markdown 内容中提取代码块和文件
 */
export function extractFilesFromMarkdown(content: string, isStreaming: boolean = false): ExtractedFile[] {
  const files: ExtractedFile[] = [];
  
  // 匹配代码块的正则表达式
  const codeBlockRegex = /```(\w+)?\s*(?:\/\/\s*(?:文件名?[:：]\s*|File[:：]\s*|Path[:：]\s*)([^\n]+))?\n([\s\S]*?)```/g;
  
  let match;
  while ((match = codeBlockRegex.exec(content)) !== null) {
    const [fullMatch, language, filename, code] = match;
    
    // 如果有明确的文件名
    if (filename) {
      files.push({
        name: filename.trim(),
        content: code.trim(),
        language: language || getLanguageFromFilename(filename.trim()),
        status: isStreaming ? 'generating' : 'completed'
      });
    } else if (language && isLikelyFileContent(code)) {
      // 如果没有文件名但代码看起来像文件内容，生成一个文件名
      const generatedName = generateFilename(language, code);
      if (generatedName) {
        files.push({
          name: generatedName,
          content: code.trim(),
          language: language,
          status: isStreaming ? 'generating' : 'completed'
        });
      }
    }
  }
  
  // 查找明确提到的文件路径
  const filePathRegex = /(?:创建|生成|保存)(?:文件|代码)[:：]?\s*`([^`]+\.[a-zA-Z0-9]+)`/g;
  while ((match = filePathRegex.exec(content)) !== null) {
    const filepath = match[1];
    // 检查是否已经有这个文件
    if (!files.some(f => f.name === filepath)) {
      // 尝试在后续内容中找到对应的代码块
      const afterMatch = content.substring(match.index);
      const nextCodeBlock = afterMatch.match(/```\w*\n([\s\S]*?)```/);
      if (nextCodeBlock) {
        files.push({
          name: filepath,
          content: nextCodeBlock[1].trim(),
          language: getLanguageFromFilename(filepath),
          status: isStreaming ? 'generating' : 'completed'
        });
      }
    }
  }
  
  return files;
}

/**
 * 根据文件名获取语言
 */
function getLanguageFromFilename(filename: string): string {
  const ext = filename.split('.').pop()?.toLowerCase();
  const languageMap: Record<string, string> = {
    'js': 'javascript',
    'jsx': 'javascript',
    'ts': 'typescript',
    'tsx': 'typescript',
    'py': 'python',
    'html': 'html',
    'css': 'css',
    'scss': 'scss',
    'sass': 'sass',
    'json': 'json',
    'xml': 'xml',
    'yaml': 'yaml',
    'yml': 'yaml',
    'md': 'markdown',
    'sql': 'sql',
    'sh': 'bash',
    'bash': 'bash',
    'java': 'java',
    'cpp': 'cpp',
    'c': 'c',
    'h': 'c',
    'go': 'go',
    'rs': 'rust',
    'php': 'php',
    'rb': 'ruby',
    'swift': 'swift',
    'kt': 'kotlin',
    'dart': 'dart',
    'vue': 'vue',
    'svelte': 'svelte'
  };
  
  return languageMap[ext || ''] || 'text';
}

/**
 * 判断代码是否像文件内容
 */
function isLikelyFileContent(code: string): boolean {
  const trimmedCode = code.trim();
  
  // 检查长度
  if (trimmedCode.length < 50) return false;
  
  // 检查是否包含典型的文件结构
  const fileIndicators = [
    // HTML 文件
    /<!DOCTYPE|<html|<head|<body/i,
    // CSS 文件
    /\{[^}]*\}/,
    // JavaScript/TypeScript 文件
    /(?:function|const|let|var|class|import|export|interface|type)\s+/,
    // Python 文件
    /(?:def|class|import|from)\s+/,
    // 配置文件
    /^\s*[\{\[]|^\s*\w+\s*[:=]/m,
    // 多行结构
    /\n.*\n.*\n/
  ];
  
  return fileIndicators.some(pattern => pattern.test(trimmedCode));
}

/**
 * 根据语言和代码内容生成文件名
 */
function generateFilename(language: string, code: string): string | null {
  const trimmedCode = code.trim();
  
  // 尝试从代码中提取名称
  let baseName = 'generated';
  
  // JavaScript/TypeScript 组件
  const componentMatch = trimmedCode.match(/(?:function|const|class)\s+([A-Z][a-zA-Z0-9]*)/);
  if (componentMatch) {
    baseName = componentMatch[1];
  }
  
  // Python 类
  const pythonClassMatch = trimmedCode.match(/class\s+([A-Z][a-zA-Z0-9]*)/);
  if (pythonClassMatch) {
    baseName = pythonClassMatch[1];
  }
  
  // HTML 标题
  const htmlTitleMatch = trimmedCode.match(/<title[^>]*>([^<]+)<\/title>/i);
  if (htmlTitleMatch) {
    baseName = htmlTitleMatch[1].replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
  }
  
  // 根据语言确定扩展名
  const extensionMap: Record<string, string> = {
    'javascript': 'js',
    'typescript': 'ts',
    'python': 'py',
    'html': 'html',
    'css': 'css',
    'json': 'json',
    'yaml': 'yaml',
    'markdown': 'md',
    'sql': 'sql',
    'bash': 'sh'
  };
  
  const extension = extensionMap[language] || 'txt';
  
  // 只有当代码看起来像文件内容时才生成文件名
  if (isLikelyFileContent(code)) {
    return `${baseName}.${extension}`;
  }
  
  return null;
}

/**
 * 实时更新文件提取状态
 */
export function updateFileExtractionStatus(
  previousFiles: ExtractedFile[],
  newContent: string,
  isStreaming: boolean
): ExtractedFile[] {
  const newFiles = extractFilesFromMarkdown(newContent, isStreaming);
  
  // 合并之前的文件和新文件
  const mergedFiles: ExtractedFile[] = [];
  const processedNames = new Set<string>();
  
  // 处理新文件
  for (const newFile of newFiles) {
    if (!processedNames.has(newFile.name)) {
      mergedFiles.push(newFile);
      processedNames.add(newFile.name);
    }
  }
  
  // 添加之前存在但新内容中不存在的文件（标记为已完成）
  for (const prevFile of previousFiles) {
    if (!processedNames.has(prevFile.name)) {
      mergedFiles.push({
        ...prevFile,
        status: 'completed'
      });
      processedNames.add(prevFile.name);
    }
  }
  
  return mergedFiles;
}
