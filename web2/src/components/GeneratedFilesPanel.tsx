import { memo, useState } from 'react';
import { ChevronDown, ChevronRight, FileText, Check, Download, Eye } from 'lucide-react';
import { cn } from '@/utils/cn';

interface GeneratedFile {
  name: string;
  content: string;
  language?: string;
  status: 'generating' | 'completed' | 'error';
}

interface GeneratedFilesPanelProps {
  files: GeneratedFile[];
  title?: string;
  version?: string;
  className?: string;
}

const FileItem = memo(({ 
  file, 
  onPreview, 
  onDownload 
}: { 
  file: GeneratedFile; 
  onPreview: (file: GeneratedFile) => void;
  onDownload: (file: GeneratedFile) => void;
}) => {
  return (
    <div className="flex items-center justify-between py-2 px-3 hover:bg-gray-50 rounded-md group">
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        <FileText className="w-4 h-4 text-gray-500 flex-shrink-0" />
        <span className="text-sm text-gray-700 truncate font-mono">
          {file.name}
        </span>
        {file.status === 'completed' && (
          <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
        )}
        {file.status === 'generating' && (
          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin flex-shrink-0" />
        )}
      </div>
      
      {file.status === 'completed' && (
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={() => onPreview(file)}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            title="预览"
          >
            <Eye className="w-4 h-4" />
          </button>
          <button
            onClick={() => onDownload(file)}
            className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            title="下载"
          >
            <Download className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  );
});

FileItem.displayName = 'FileItem';

export const GeneratedFilesPanel = memo(({
  files,
  title = "生成文件",
  version = "v1",
  className
}: GeneratedFilesPanelProps) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const handlePreview = (file: GeneratedFile) => {
    // 创建一个新窗口来预览文件
    const newWindow = window.open('', '_blank');
    if (newWindow) {
      newWindow.document.write(`
        <html>
          <head>
            <title>${file.name}</title>
            <style>
              body { 
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; 
                margin: 20px; 
                background: #f8f9fa;
              }
              pre { 
                background: white; 
                padding: 20px; 
                border-radius: 8px; 
                border: 1px solid #e9ecef;
                overflow-x: auto;
              }
              .header {
                background: white;
                padding: 15px 20px;
                border-radius: 8px;
                border: 1px solid #e9ecef;
                margin-bottom: 15px;
              }
            </style>
          </head>
          <body>
            <div class="header">
              <h2>${file.name}</h2>
              <p>语言: ${file.language || '未知'}</p>
            </div>
            <pre><code>${file.content.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</code></pre>
          </body>
        </html>
      `);
      newWindow.document.close();
    }
  };

  const handleDownload = (file: GeneratedFile) => {
    const blob = new Blob([file.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (files.length === 0) {
    return null;
  }

  return (
    <div className={cn(
      "bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden my-4",
      className
    )}>
      {/* 头部 */}
      <div className="flex items-center justify-between px-4 py-3 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="flex items-center space-x-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
          >
            {isCollapsed ? (
              <ChevronRight className="w-4 h-4" />
            ) : (
              <ChevronDown className="w-4 h-4" />
            )}
            <span>{title}</span>
          </button>
          <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
            {version}
          </span>
        </div>
        <div className="text-xs text-gray-500">
          {files.filter(f => f.status === 'completed').length} / {files.length} 完成
        </div>
      </div>

      {/* 文件列表 */}
      <div className={cn(
        "transition-all duration-300 ease-in-out",
        isCollapsed ? "max-h-0 overflow-hidden" : "max-h-none"
      )}>
        <div className="p-2">
          {files.map((file, index) => (
            <FileItem
              key={`${file.name}-${index}`}
              file={file}
              onPreview={handlePreview}
              onDownload={handleDownload}
            />
          ))}
        </div>
      </div>

      {isCollapsed && (
        <div className="px-4 py-2 text-xs text-gray-500 bg-gray-50">
          {files.length} 个文件已生成
        </div>
      )}
    </div>
  );
});

GeneratedFilesPanel.displayName = 'GeneratedFilesPanel';
