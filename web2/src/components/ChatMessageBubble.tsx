import type { Message } from 'ai/react';
import { useState, useEffect } from 'react';
import { MemoizedMarkdown } from './MemoizedMarkdown';
import { GeneratedFilesPanel } from './GeneratedFilesPanel';
import { extractFilesFromMarkdown, updateFileExtractionStatus } from '@/utils/fileExtractor';
import { cn } from '@/utils/cn';
import { Bot, User, Loader2 } from 'lucide-react';

interface ChatMessageBubbleProps {
  message: Message;
  aiEmoji?: string;
  isStreaming?: boolean;
}

function StreamingCursor() {
  return (
    <span className="inline-block w-2 h-5 bg-blue-500 animate-pulse ml-1 align-text-bottom"></span>
  );
}

function MessageAvatar({ role, emoji }: { role: string; emoji?: string }) {
  if (role === 'user') {
    return (
      <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-3">
        <User className="w-4 h-4 text-white" />
      </div>
    );
  }

  return (
    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center mr-3">
      {emoji ? (
        <span className="text-sm">{emoji}</span>
      ) : (
        <Bot className="w-4 h-4 text-white" />
      )}
    </div>
  );
}

export function ChatMessageBubble({
  message,
  aiEmoji,
  isStreaming = false
}: ChatMessageBubbleProps) {
  const [displayedContent, setDisplayedContent] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [extractedFiles, setExtractedFiles] = useState<any[]>([]);

  // 打字机效果
  useEffect(() => {
    if (message.role === 'assistant' && isStreaming && message.content) {
      setIsTyping(true);
      let currentIndex = 0;
      const content = message.content;

      const typeInterval = setInterval(() => {
        if (currentIndex < content.length) {
          const currentContent = content.slice(0, currentIndex + 1);
          setDisplayedContent(currentContent);

          // 实时更新文件提取
          const newFiles = updateFileExtractionStatus(extractedFiles, currentContent, true);
          setExtractedFiles(newFiles);

          currentIndex++;
        } else {
          setIsTyping(false);
          clearInterval(typeInterval);

          // 最终更新文件状态
          const finalFiles = extractFilesFromMarkdown(content, false);
          setExtractedFiles(finalFiles);
        }
      }, 20); // 调整打字速度

      return () => clearInterval(typeInterval);
    } else {
      setDisplayedContent(message.content);
      setIsTyping(false);

      // 提取文件
      if (message.role === 'assistant' && message.content) {
        const files = extractFilesFromMarkdown(message.content, false);
        setExtractedFiles(files);
      }
    }
  }, [message.content, isStreaming, message.role]);

  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  return (
    <div className="mb-6">
      <div
        className={cn(
          "flex items-start max-w-[100%]",
          isUser ? "justify-end" : "justify-start"
        )}
      >
        {/* AI头像 */}
        {!isUser && (
          <MessageAvatar role={message.role} emoji={aiEmoji} />
        )}

        {/* 消息内容 */}
        <div
          className={cn(
            "rounded-2xl px-4 py-3 max-w-[80%] relative",
            isUser
              ? "bg-blue-500 text-white ml-auto mr-3"
              : "bg-white/80 backdrop-blur-sm border border-gray-200 shadow-sm"
          )}
        >
          <div className={cn(
            "chat-message-bubble whitespace-pre-wrap flex flex-col prose max-w-none overflow-x-auto",
            isUser && "prose-invert"
          )}>
            <MemoizedMarkdown
              content={isStreaming && isAssistant ? displayedContent : message.content}
              id={message.id}
              isStreaming={isStreaming && isAssistant}
            />

            {/* 生成的文件面板 */}
            {isAssistant && extractedFiles.length > 0 && (
              <div className="mt-4 not-prose">
                <GeneratedFilesPanel
                  files={extractedFiles}
                  title="生成文件"
                  version="v1"
                />
              </div>
            )}

            {/* 流式输入光标 */}
            {isTyping && isAssistant && <StreamingCursor />}

            {/* 加载指示器 */}
            {isStreaming && isAssistant && !message.content && (
              <div className="flex items-center space-x-2 text-gray-500">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm">正在生成回复...</span>
              </div>
            )}
          </div>
        </div>

        {/* 用户头像 */}
        {isUser && (
          <MessageAvatar role={message.role} />
        )}
      </div>
    </div>
  );
}
