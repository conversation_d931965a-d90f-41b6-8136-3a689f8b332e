import { memo, useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { ChevronDown, ChevronRight, Copy, Check, FileText } from 'lucide-react';
import { cn } from '@/utils/cn';

// 可折叠代码块组件
const CollapsibleCodeBlock = memo(({
  children,
  className,
  language,
  filename,
  isStreaming = false
}: {
  children: string;
  className?: string;
  language?: string;
  filename?: string;
  isStreaming?: boolean;
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [copied, setCopied] = useState(false);

  // 检测是否是文件内容（通常比较长）
  const isLargeContent = children.length > 500;
  const shouldShowCollapse = isLargeContent && !isStreaming;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(children);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  // 从类名中提取语言
  const detectedLanguage = language || className?.replace('language-', '') || 'text';

  // 如果有文件名或者是大内容，显示文件头部
  const showHeader = filename || shouldShowCollapse || detectedLanguage !== 'text';

  return (
    <div className="my-4 rounded-lg border border-gray-200 bg-gray-50 overflow-hidden">
      {showHeader && (
        <div className="flex items-center justify-between px-4 py-2 bg-gray-100 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            {shouldShowCollapse && (
              <button
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                {isCollapsed ? (
                  <ChevronRight className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </button>
            )}
            <FileText className="w-4 h-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">
              {filename || `${detectedLanguage.toUpperCase()} 代码`}
            </span>
            {isStreaming && (
              <span className="text-xs text-blue-600 animate-pulse">生成中...</span>
            )}
          </div>
          <button
            onClick={handleCopy}
            className="flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700 transition-colors"
          >
            {copied ? (
              <>
                <Check className="w-4 h-4" />
                <span>已复制</span>
              </>
            ) : (
              <>
                <Copy className="w-4 h-4" />
                <span>复制</span>
              </>
            )}
          </button>
        </div>
      )}

      <div className={cn(
        "transition-all duration-300 ease-in-out",
        isCollapsed ? "max-h-0 overflow-hidden" : "max-h-none"
      )}>
        <pre className="p-4 overflow-x-auto text-sm bg-gray-50">
          <code className={className}>{children}</code>
        </pre>
      </div>

      {isCollapsed && (
        <div className="px-4 py-2 text-xs text-gray-500 bg-gray-50">
          点击展开查看完整代码 ({children.split('\n').length} 行)
        </div>
      )}
    </div>
  );
});

CollapsibleCodeBlock.displayName = 'CollapsibleCodeBlock';

const MemoizedMarkdownBlock = memo(
  ({ content, isStreaming = false }: { content: string; isStreaming?: boolean }) => {
    const components = {
      // 表格组件
      table: ({ node, ...props }: any) => (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 my-4" {...props} />
        </div>
      ),
      thead: ({ node, ...props }: any) => (
        <thead className="bg-gray-50" {...props} />
      ),
      th: ({ node, ...props }: any) => (
        <th
          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border border-gray-200"
          {...props}
        />
      ),
      td: ({ node, ...props }: any) => (
        <td
          className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 border border-gray-200"
          {...props}
        />
      ),
      tr: ({ node, ...props }: any) => (
        <tr className="odd:bg-white even:bg-gray-50" {...props} />
      ),

      // 增强的代码块组件
      code: ({ node, inline, className, children, ...props }: any) => {
        const match = /language-(\w+)/.exec(className || '');
        const language = match ? match[1] : '';

        // 内联代码
        if (inline) {
          return (
            <code
              className="px-1.5 py-0.5 text-sm bg-gray-100 text-gray-800 rounded font-mono"
              {...props}
            >
              {children}
            </code>
          );
        }

        // 代码块
        const codeContent = String(children).replace(/\n$/, '');

        // 尝试从代码内容中提取文件名
        const lines = codeContent.split('\n');
        let filename = '';
        let actualContent = codeContent;

        // 检查第一行是否包含文件名注释
        if (lines.length > 0) {
          const firstLine = lines[0].trim();
          if (firstLine.startsWith('//') || firstLine.startsWith('#') || firstLine.startsWith('<!--')) {
            const filenameMatch = firstLine.match(/(?:文件名?[:：]\s*|File[:：]\s*|Path[:：]\s*)([^\s]+)/i);
            if (filenameMatch) {
              filename = filenameMatch[1];
              actualContent = lines.slice(1).join('\n');
            }
          }
        }

        return (
          <CollapsibleCodeBlock
            className={className}
            language={language}
            filename={filename}
            isStreaming={isStreaming}
          >
            {actualContent}
          </CollapsibleCodeBlock>
        );
      },
    };

    return <ReactMarkdown remarkPlugins={[remarkGfm]} components={components}>{content}</ReactMarkdown>;
  },
  (prevProps, nextProps) => {
    if (prevProps.content !== nextProps.content) return false;
    if (prevProps.isStreaming !== nextProps.isStreaming) return false;
    return true;
  },
);

MemoizedMarkdownBlock.displayName = 'MemoizedMarkdownBlock';

export const MemoizedMarkdown = memo(({
  content,
  id,
  isStreaming = false
}: {
  content: string;
  id: string;
  isStreaming?: boolean;
}) => {
  return <MemoizedMarkdownBlock content={content} isStreaming={isStreaming} key={id} />;
});

MemoizedMarkdown.displayName = 'MemoizedMarkdown';
