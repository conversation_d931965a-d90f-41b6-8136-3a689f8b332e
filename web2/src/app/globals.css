@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
.chat-message-bubble {
  /* 确保代码块在消息气泡中正确显示 */
}

.chat-message-bubble .not-prose {
  /* 重置 prose 样式对文件面板的影响 */
  color: inherit;
  font-size: inherit;
  line-height: inherit;
}

.chat-message-bubble .not-prose * {
  margin: 0;
  padding: 0;
}

@layer base {
  :root {
    --background: 248 250 252;
    --foreground: 15 23 42;
    --muted: 241 245 249;
    --muted-foreground: 100 116 139;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;
    --border: 226 232 240;
    --input: 226 232 240;
    --card: 255 255 255;
    --card-foreground: 15 23 42;
    --primary: 59 130 246;
    --primary-foreground: 248 250 252;
    --secondary: 241 245 249;
    --secondary-foreground: 15 23 42;
    --accent: 139 92 246;
    --accent-foreground: 248 250 252;
    --destructive: 239 68 68;
    --destructive-foreground: 248 250 252;
    --ring: 59 130 246;
    --radius: 0.75rem;
  }

}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply font-sans antialiased bg-background text-foreground;
  }

  p {
    margin: 8px 0;
  }

  code {
    @apply text-orange-700;
  }

  li {
    padding: 4px;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .chat-message-bubble a {
    text-decoration: underline;
  }

  .chat-message-bubble ol {
    list-style: decimal;
  }

  .chat-message-bubble ul {
    list-style: disc;
  }

  .chat-message-bubble li {
    white-space: normal;
  }

  .chat-message-bubble pre {
    white-space: pre;
    overflow-x: auto;
  }

  .chat-message-bubble blockquote {
    border-left: 4px solid #474545;
    padding-left: 16px;
  }

  .chat-message-bubble hr {
    border-top: 1px solid #474545;
  }

  .chat-message-bubble h1 {
    font-size: x-large;
    font-weight: bold;
  }

  .chat-message-bubble h2 {
    font-size: larger;
    font-weight: bold;
  }

  .chat-message-bubble h3 {
    font-size: large;
    font-weight: bold;
  }

  .chat-message-bubble h4 {
    font-size: medium;
    font-weight: bold;
  }

  .chat-message-bubble h5 {
    font-size: small;
    font-weight: bold;
  }

  .chat-message-bubble h6 {
    font-size: smaller;
    font-weight: bold;
  }

  .chat-message-bubble del {
    text-decoration: line-through;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.6s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    to {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
    }
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .chat-input-shadow {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}
